{"manifest": {"name": "@webassemblyjs/helper-code-frame", "version": "1.7.11", "description": "", "main": "lib/index.js", "module": "esm/index.js", "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "author": {"name": "<PERSON>"}, "license": "MIT", "dependencies": {"@webassemblyjs/wast-printer": "1.7.11"}, "devDependencies": {"@webassemblyjs/ast": "1.7.11"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909", "_registry": "npm", "_loc": "C:\\Users\\<USER>\\OneDrive\\Desktop\\txData\\QBCore_847FC7.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-helper-code-frame-1.7.11-cf8f106e746662a0da29bdef635fcd3d1248364b-integrity\\node_modules\\@webassemblyjs\\helper-code-frame\\package.json", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.7.11.tgz#cf8f106e746662a0da29bdef635fcd3d1248364b", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.7.11.tgz", "hash": "cf8f106e746662a0da29bdef635fcd3d1248364b", "integrity": "sha512-T8ESC9KMXFTXA5urJcyor5cn6qWeZ4/zLPyWeEXZ03hj/x9weSokGNkVCdnhSabKGYWxElSdgJ+sFa9G/RdHNw==", "registry": "npm", "packageName": "@webassemblyjs/helper-code-frame", "cacheIntegrity": "sha512-T8ESC9KMXFTXA5urJcyor5cn6qWeZ4/zLPyWeEXZ03hj/x9weSokGNkVCdnhSabKGYWxElSdgJ+sFa9G/RdHNw== sha1-z48QbnRmYqDaKb3vY1/NPRJINks="}, "registry": "npm", "hash": "cf8f106e746662a0da29bdef635fcd3d1248364b"}