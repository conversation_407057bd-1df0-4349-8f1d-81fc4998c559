{"name": "@webassemblyjs/helper-wasm-bytecode", "version": "1.7.11", "description": "WASM's Bytecode constants", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}