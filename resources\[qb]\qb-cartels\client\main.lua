local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local currentCartel = nil
local captureProgress = 0
local isCapturing = false
local captureStartTime = 0
local inWarzone = false

-- Initialize
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            PlayerData = QBCore.Functions.GetPlayerData()
            break
        end
        Wait(1000)
    end
    
    -- Create cartel blips
    CreateCartelBlips()
    CreateWarzoneBlip()
end)

-- Events
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    CreateCartelBlips()
    CreateWarzoneBlip()
end)

RegisterNetEvent('QBCore:Client:OnPlayerUnload', function()
    PlayerData = {}
    RemoveCartelBlips()
end)

RegisterNetEvent('qb-cartels:client:UpdateCartelOwner', function(cartelId, gangName)
    if Config.Cartels[cartelId] then
        Config.Cartels[cartelId].owner = gangName
        UpdateCartelBlip(cartelId)
    end
end)

-- Cartel Blip Management
local cartelBlips = {}
local warzoneBlip = nil

function CreateCartelBlips()
    for cartelId, cartelData in pairs(Config.Cartels) do
        local blip = AddBlipForCoord(cartelData.coords.x, cartelData.coords.y, cartelData.coords.z)
        SetBlipSprite(blip, cartelData.blip.sprite)
        SetBlipColour(blip, cartelData.blip.color)
        SetBlipScale(blip, cartelData.blip.scale)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(cartelData.label)
        EndTextCommandSetBlipName(blip)
        
        cartelBlips[cartelId] = blip
    end
end

function CreateWarzoneBlip()
    local warzone = Config.Warzone
    warzoneBlip = AddBlipForCoord(warzone.coords.x, warzone.coords.y, warzone.coords.z)
    SetBlipSprite(warzoneBlip, warzone.blip.sprite)
    SetBlipColour(warzoneBlip, warzone.blip.color)
    SetBlipScale(warzoneBlip, warzone.blip.scale)
    SetBlipAlpha(warzoneBlip, warzone.blip.alpha)
    SetBlipAsShortRange(warzoneBlip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString("Warzone")
    EndTextCommandSetBlipName(warzoneBlip)
end

function UpdateCartelBlip(cartelId)
    local cartel = Config.Cartels[cartelId]
    local blip = cartelBlips[cartelId]
    
    if blip and cartel then
        if cartel.owner then
            SetBlipColour(blip, 2) -- Green for owned
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(cartel.label .. " (" .. cartel.owner .. ")")
            EndTextCommandSetBlipName(blip)
        else
            SetBlipColour(blip, cartel.blip.color)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(cartel.label)
            EndTextCommandSetBlipName(blip)
        end
    end
end

function RemoveCartelBlips()
    for _, blip in pairs(cartelBlips) do
        RemoveBlip(blip)
    end
    cartelBlips = {}
    
    if warzoneBlip then
        RemoveBlip(warzoneBlip)
        warzoneBlip = nil
    end
end

-- Cartel Capture System
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local wasInWarzone = inWarzone
            inWarzone = false
            
            -- Check if in warzone
            local distance = #(playerCoords - Config.Warzone.coords)
            if distance <= Config.Warzone.radius then
                inWarzone = true
                if not wasInWarzone then
                    QBCore.Functions.Notify('Entered Warzone - KOS Rules Apply', 'error')
                    TriggerEvent('qb-cartels:client:EnteredWarzone')
                end
            elseif wasInWarzone then
                QBCore.Functions.Notify('Left Warzone', 'success')
                TriggerEvent('qb-cartels:client:LeftWarzone')
            end
            
            -- Check cartel proximity
            local nearCartel = nil
            for cartelId, cartelData in pairs(Config.Cartels) do
                local cartelDistance = #(playerCoords - cartelData.coords)
                if cartelDistance <= cartelData.radius then
                    nearCartel = cartelId
                    break
                end
            end
            
            if nearCartel ~= currentCartel then
                if currentCartel then
                    -- Left cartel area
                    TriggerEvent('qb-cartels:client:LeftCartel', currentCartel)
                    if isCapturing then
                        StopCapture()
                    end
                end
                
                if nearCartel then
                    -- Entered cartel area
                    TriggerEvent('qb-cartels:client:EnteredCartel', nearCartel)
                end
                
                currentCartel = nearCartel
            end
            
            -- Handle capture progress
            if isCapturing and currentCartel then
                UpdateCaptureProgress()
            end
        end
        Wait(1000)
    end
end)

RegisterNetEvent('qb-cartels:client:EnteredCartel', function(cartelId)
    local cartel = Config.Cartels[cartelId]
    QBCore.Functions.Notify('Entered ' .. cartel.name .. ' capture zone', 'primary')
    
    -- Check if player can capture
    if CanPlayerCapture() then
        QBCore.Functions.Notify('Press [E] to start capturing', 'primary')
    end
end)

RegisterNetEvent('qb-cartels:client:LeftCartel', function(cartelId)
    local cartel = Config.Cartels[cartelId]
    QBCore.Functions.Notify('Left ' .. cartel.name .. ' capture zone', 'primary')
end)

-- Capture Controls
CreateThread(function()
    while true do
        if currentCartel and not isCapturing then
            if IsControlJustPressed(0, 38) then -- E key
                if CanPlayerCapture() then
                    StartCapture()
                end
            end
        end
        Wait(0)
    end
end)

function CanPlayerCapture()
    -- Check if player has gang
    if not PlayerData.gang or PlayerData.gang.name == 'none' then
        return false
    end
    
    -- Check if player has required weapon
    local playerPed = PlayerPedId()
    local currentWeapon = GetSelectedPedWeapon(playerPed)
    
    local hasValidWeapon = false
    for _, weapon in ipairs(Config.GangRequirements.requiredWeapons) do
        if GetHashKey(weapon) == currentWeapon then
            hasValidWeapon = true
            break
        end
    end
    
    if not hasValidWeapon then
        QBCore.Functions.Notify('You need a 5.56 caliber weapon or greater to capture', 'error')
        return false
    end
    
    -- Check if player is in vehicle
    if IsPedInAnyVehicle(playerPed, false) then
        QBCore.Functions.Notify('You cannot capture from inside a vehicle', 'error')
        return false
    end
    
    return true
end

function StartCapture()
    if not currentCartel then return end
    
    isCapturing = true
    captureProgress = 0
    captureStartTime = GetGameTimer()
    
    TriggerServerEvent('qb-cartels:server:StartCapture', currentCartel)
    QBCore.Functions.Notify('Started capturing ' .. Config.Cartels[currentCartel].name, 'success')
end

function StopCapture()
    if not isCapturing then return end
    
    isCapturing = false
    captureProgress = 0
    captureStartTime = 0
    
    TriggerServerEvent('qb-cartels:server:StopCapture', currentCartel)
    QBCore.Functions.Notify('Stopped capturing', 'error')
end

function UpdateCaptureProgress()
    if not isCapturing or not currentCartel then return end
    
    local cartel = Config.Cartels[currentCartel]
    local elapsed = (GetGameTimer() - captureStartTime) / 1000
    local totalTime = cartel.captureTime
    
    captureProgress = math.min(elapsed / totalTime * 100, 100)
    
    -- Update progress bar (you would implement your own progress bar here)
    -- For now, just show text
    if captureProgress >= 100 then
        CompleteCapture()
    end
end

function CompleteCapture()
    isCapturing = false
    TriggerServerEvent('qb-cartels:server:CompleteCapture', currentCartel)
    QBCore.Functions.Notify('Cartel captured!', 'success')
end

-- Cartel Notifications
RegisterNetEvent('qb-cartels:client:CartelCaptured', function(cartelId, gangName)
    local cartel = Config.Cartels[cartelId]
    QBCore.Functions.Notify(gangName .. ' has captured ' .. cartel.name, 'primary')
end)

RegisterNetEvent('qb-cartels:client:CartelContested', function(cartelId)
    local cartel = Config.Cartels[cartelId]
    QBCore.Functions.Notify(cartel.name .. ' is being contested!', 'error')
end)

-- APD Notification
RegisterNetEvent('qb-cartels:client:NotifyAPD', function(cartelId)
    if PlayerData.job and PlayerData.job.name == 'police' then
        local cartel = Config.Cartels[cartelId]
        QBCore.Functions.Notify('Gang activity detected at ' .. cartel.name, 'error')
        
        -- Create temporary blip for APD
        local blip = AddBlipForCoord(cartel.coords.x, cartel.coords.y, cartel.coords.z)
        SetBlipSprite(blip, 161)
        SetBlipColour(blip, 1)
        SetBlipScale(blip, 1.2)
        SetBlipFlashes(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString("Gang Activity - " .. cartel.name)
        EndTextCommandSetBlipName(blip)
        
        -- Remove blip after 5 minutes
        SetTimeout(300000, function()
            RemoveBlip(blip)
        end)
    end
end)

-- Exports
exports('IsInWarzone', function()
    return inWarzone
end)

exports('GetCurrentCartel', function()
    return currentCartel
end)

exports('IsCapturing', function()
    return isCapturing
end)
