{"manifest": {"name": "@webassemblyjs/helper-fsm", "version": "1.9.0", "description": "FSM implementation", "main": "lib/index.js", "module": "esm/index.js", "author": {"name": "<PERSON><PERSON>"}, "license": "ISC", "publishConfig": {"access": "public"}, "gitHead": "0440b420888c1f7701eb9762ec657775506b87d8", "_registry": "npm", "_loc": "C:\\Users\\<USER>\\OneDrive\\Desktop\\txData\\QBCore_847FC7.base\\cache\\yarn-cache\\v6\\npm-@webassemblyjs-helper-fsm-1.9.0-c05256b71244214671f4b08ec108ad63b70eddb8-integrity\\node_modules\\@webassemblyjs\\helper-fsm\\package.json", "licenseText": "MIT License\n\nCopyright (c) 2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n"}, "artifacts": [], "remote": {"resolved": "https://registry.yarnpkg.com/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8", "type": "tarball", "reference": "https://registry.yarnpkg.com/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "hash": "c05256b71244214671f4b08ec108ad63b70eddb8", "integrity": "sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw==", "registry": "npm", "packageName": "@webassemblyjs/helper-fsm", "cacheIntegrity": "sha512-OPRowhGbshCb5PxJ8LocpdX9Kl0uB4XsAjl6jH/dWKlk/mzsANvhwbiULsaiqT5GZGT9qinTICdj6PLuM5gslw== sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg="}, "registry": "npm", "hash": "c05256b71244214671f4b08ec108ad63b70eddb8"}