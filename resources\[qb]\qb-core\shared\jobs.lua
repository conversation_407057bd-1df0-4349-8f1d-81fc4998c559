QBShared = QBShared or {}
QBShared.ForceJobDefaultDutyAtLogin = true -- true: Force duty state to jobdefaultDuty | false: set duty state from database last saved

-- Olympus Altis Life Faction System
QBShared.Jobs = {
	-- Civilian Jobs (Legal)
	unemployed = { label = 'Civilian', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Civilian', payment = 0 } } },
	taxi = { label = 'Taxi Driver', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Driver', payment = 25 } } },
	bus = { label = 'Bus Driver', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Driver', payment = 30 } } },
	trucker = { label = 'Trucker', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Driver', payment = 35 } } },
	garbage = { label = 'Garbage Collector', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Collector', payment = 40 } } },

	-- Legal Processing Jobs
	salt = { label = 'Salt Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 20 } } },
	sand = { label = 'Sand Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 20 } } },
	iron = { label = 'Iron Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 25 } } },
	copper = { label = 'Copper Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 25 } } },
	diamond = { label = 'Diamond Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 50 } } },
	oil = { label = 'Oil Processor', defaultDuty = true, offDutyPay = false, grades = { ['0'] = { name = 'Worker', payment = 30 } } },

	-- Altis Police Department (APD) - Olympus Style
	police = {
		label = 'Altis Police Department',
		type = 'leo',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Deputy', payment = 75, minTime = 1800 }, -- Entry level, 1800 minutes on server
			['1'] = { name = 'Patrol Officer', payment = 100, minTime = 1200, timeInGrade = 7 }, -- 1200 minutes + 7 days TIG
			['2'] = { name = 'Corporal', payment = 125, minTime = 10000, timeInGrade = 30, canLethal = true }, -- First lethal loading officer
			['3'] = { name = 'Sergeant', payment = 150, canInterview = true }, -- Interviews applicants
			['4'] = { name = 'Lieutenant', payment = 175, canAcceptApps = true }, -- Accepts applications
			['5'] = { name = 'Captain', payment = 200, sectionHead = true }, -- Section head
			['6'] = { name = 'Deputy Chief', payment = 225, advisor = true }, -- Advisor to Chief
			['7'] = { name = 'Chief of Police', isboss = true, payment = 250, finalSay = true }, -- Final say on all APD matters
		},
		equipment = {
			weapons = { 'weapon_pistol', 'weapon_stungun', 'weapon_nightstick', 'weapon_flashlight' },
			vehicles = { 'police', 'police2', 'police3', 'policeb' },
			specialUnits = { 'SWAT', 'FTO', 'Undercover' }
		},
		rules = {
			waveSystem = true,
			federalEventResponse = true,
			lethalAuthorization = { minRank = 2 }, -- Corporal+
			processingTime = 15, -- 15 minutes max
			threeToOneRule = true
		}
	},
	-- Rescue & Recovery (R&R) - Olympus Style Medical Faction
	ambulance = {
		label = 'Rescue & Recovery',
		type = 'ems',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Trainee', payment = 50 },
			['1'] = { name = 'Paramedic', payment = 75 },
			['2'] = { name = 'Advanced Paramedic', payment = 100 },
			['3'] = { name = 'Senior Paramedic', payment = 125 },
			['4'] = { name = 'Field Training Officer', payment = 150, canTrain = true },
			['5'] = { name = 'Supervisor', payment = 175, canSupervise = true },
			['6'] = { name = 'Chief of Medicine', isboss = true, payment = 200 },
		},
		equipment = {
			vehicles = { 'ambulance', 'rnr_heli', 'rnr_boat' },
			medical = { 'defibrillator', 'bloodbag', 'bandage', 'morphine' }
		},
		rules = {
			neutrality = true, -- Cannot be killed except in specific zones
			reviveOnly = true, -- Primary function is revival
			noWeapons = true, -- Cannot carry weapons
			specialZoneRules = true -- Different rules in red zones
		}
	},
	-- Vigilante License Holders
	vigilante = {
		label = 'Vigilante',
		type = 'vigilante',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Tier 1 Vigilante', payment = 0, weapons = { 'weapon_pistol', 'weapon_stungun' } },
			['1'] = { name = 'Tier 2 Vigilante', payment = 0, weapons = { 'weapon_pistol', 'weapon_stungun', 'weapon_carbinerifle' } },
			['2'] = { name = 'Tier 3 Vigilante', payment = 0, weapons = { 'weapon_pistol', 'weapon_stungun', 'weapon_carbinerifle' } },
			['3'] = { name = 'Tier 4 Vigilante', payment = 0, weapons = { 'weapon_pistol', 'weapon_stungun', 'weapon_carbinerifle', 'weapon_smg' } },
			['4'] = { name = 'Tier 5 Vigilante', payment = 0, weapons = { 'weapon_pistol', 'weapon_stungun', 'weapon_carbinerifle', 'weapon_smg', 'weapon_assaultrifle' } },
		},
		rules = {
			bountyHunting = true,
			arrestPowers = true,
			weaponRestrictions = true,
			licenseRequired = true
		}
	},

	-- Workers Protection License
	wpl = {
		label = 'Workers Protection',
		type = 'wpl',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Protected Worker', payment = 0, weapons = { 'weapon_carbinerifle', 'weapon_smg' } },
		},
		rules = {
			legalJobsOnly = true,
			noIllegalActivities = true,
			weaponRestrictions = true
		}
	},
	-- Support Factions
	support = {
		label = 'Support Team',
		type = 'support',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Support Member', payment = 0 },
			['1'] = { name = 'Senior Support', payment = 0 },
			['2'] = { name = 'Support Supervisor', isboss = true, payment = 0 },
		},
		rules = {
			adminPowers = true,
			playerAssistance = true,
			ruleEnforcement = true
		}
	},

	-- Staff Team
	staff = {
		label = 'Staff Team',
		type = 'staff',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Moderator', payment = 0 },
			['1'] = { name = 'Administrator', payment = 0 },
			['2'] = { name = 'Senior Administrator', payment = 0 },
			['3'] = { name = 'Head Administrator', isboss = true, payment = 0 },
		},
		rules = {
			fullAdminPowers = true,
			serverManagement = true,
			eventControl = true
		}
	},

	-- Civilian Council
	civiliancouncil = {
		label = 'Civilian Council',
		type = 'council',
		defaultDuty = true,
		offDutyPay = false,
		grades = {
			['0'] = { name = 'Council Member', payment = 0 },
			['1'] = { name = 'Senior Council Member', payment = 0 },
			['2'] = { name = 'Council Leader', isboss = true, payment = 0 },
		},
		rules = {
			civilianRepresentation = true,
			policyInfluence = true,
			communityLiaison = true
		}
	},
}
