Config = {}

-- Federal Events Configuration (Olympus Style)
Config.FederalEvents = {
    ['federal_reserve'] = {
        name = 'Federal Reserve',
        label = 'Federal Reserve Robbery',
        priority = 2, -- Second highest priority
        coords = vector3(5000.0, -5200.0, 2.0),
        
        -- Vault domes
        vaults = {
            {
                coords = vector3(4980.0, -5180.0, 15.0),
                radius = 25.0,
                door = vector3(4975.0, -5175.0, 15.0)
            },
            {
                coords = vector3(5020.0, -5220.0, 15.0),
                radius = 25.0,
                door = vector3(5025.0, -5225.0, 15.0)
            }
        },
        
        -- Anti-air system
        antiAir = {
            coords = vector3(4990.0, -5190.0, 15.0),
            outerRadius = 500.0,
            innerRadius = 200.0,
            hackable = true
        },
        
        requirements = {
            minCops = 5,
            items = {
                blastingCharge = 1,
                boltCutters = 1,
                hackingDevice = 1 -- Optional for anti-air
            }
        },
        
        timing = {
            bombTimer = 1200, -- 20 minutes
            cooldownSuccess = 3000, -- 50 minutes
            cooldownFailure = 2100  -- 35 minutes
        },
        
        rewards = {
            goldBars = {
                base = { min = 150, max = 225 },
                perCop = {
                    corporal = 5,  -- +5 bars per Corporal+
                    sergeant = 7   -- +7 bars per Sergeant+
                }
            },
            apdDefuseReward = 500000 -- Split between officers within 3km
        },
        
        goldTraders = {
            vector3(-1500.0, 4500.0, 50.0), -- North trader
            vector3(2000.0, -1500.0, 30.0), -- East trader  
            vector3(-2000.0, -1000.0, 25.0) -- West trader
        }
    },
    
    ['blackwater_armory'] = {
        name = 'Blackwater Armory',
        label = 'Blackwater Weapons Facility',
        priority = 1, -- Highest priority
        coords = vector3(-2300.0, 3200.0, 32.0),
        
        requirements = {
            minCops = 5,
            items = {
                blastingCharge = 1,
                boltCutters = 1
            }
        },
        
        timing = {
            bombTimer = 1200, -- 20 minutes
            cooldownSuccess = 3000,
            cooldownFailure = 2100
        },
        
        rewards = {
            weapons = {
                'weapon_assaultrifle',
                'weapon_carbinerifle',
                'weapon_specialcarbine',
                'weapon_bullpuprifle',
                'weapon_advancedrifle',
                'weapon_sniperrifle'
            },
            equipment = {
                'armor',
                'nightvision',
                'thermal_scope'
            }
        }
    },
    
    ['altis_penitentiary'] = {
        name = 'Altis Penitentiary',
        label = 'Jail Break',
        priority = 4, -- Lowest priority
        coords = vector3(1700.0, 2600.0, 45.0),
        
        requirements = {
            minCops = 5,
            items = {
                blastingCharge = 1,
                boltCutters = 1
            }
        },
        
        timing = {
            bombTimer = 1200,
            cooldownSuccess = 3000,
            cooldownFailure = 2100
        },
        
        rewards = {
            prisonerRelease = true,
            bountyReduction = 0.5 -- 50% bounty reduction for participants
        }
    },
    
    ['evidence_lockup'] = {
        name = 'Evidence Lockup',
        label = 'Evidence Lockup Robbery',
        priority = 3,
        coords = vector3(400.0, -1600.0, 29.0),
        blueZone = true, -- Follows blue zone rules
        
        requirements = {
            minCops = 5,
            items = {
                blastingCharge = 1
            }
        },
        
        timing = {
            bombTimer = 1200,
            cooldownSuccess = 3000,
            cooldownFailure = 2100
        },
        
        rewards = {
            evidence = true,
            contraband = {
                'cocaine',
                'meth',
                'weed',
                'heroin',
                'weapons'
            }
        }
    }
}

-- APD Response Rules
Config.APDResponse = {
    mandatoryResponse = true,
    minimumOfficers = 4, -- If 4 or less, not required to respond (except jail)
    jailAlwaysRequired = true, -- Must always respond to jail
    
    waveRules = {
        enabled = true,
        exceptions = {
            bank = 'five_or_less_officers', -- No waves at bank if 5 or less officers
            evidenceLockup = 'no_waves' -- Evidence lockup doesn't follow wave rules
        }
    },
    
    priority = {
        'blackwater_armory',
        'federal_reserve', 
        'evidence_lockup',
        'altis_penitentiary'
    },
    
    bombPlantedPriority = true -- Event with bomb planted takes priority
}

-- Engagement Rules
Config.EngagementRules = {
    noRPRequired = true, -- No RP required to engage federal event participants
    kosZone = true, -- Federal events become KOS zones when active
    vehicleSeizure = {
        leftAtEvent = true, -- Vehicles left at event can be seized
        followedOut = true  -- Vehicles followed out can be seized if caught
    }
}

-- Equipment Authorization
Config.EquipmentAuth = {
    tenOrLessOfficers = {
        deputyPOLethals = true, -- Deputies/POs can use lethals
        sparMK20Deputies = true, -- Spar and MK20 for Deputies
        patrolOfficerVests = true -- Tier 3 vests for POs
    },
    
    extremeDisadvantage = {
        type115Corporals = true, -- Type 115s for Corporals
        authorizedBy = { 'FTO', 'Staff SGT+' }
    },
    
    tenMinutesLeft = {
        deputyPOLethals = true -- When 10 minutes left on bomb
    }
}

-- Bomb Mechanics
Config.BombMechanics = {
    plantingTime = 30, -- 30 seconds to plant
    defusingTime = 45, -- 45 seconds to defuse
    timer = 1200, -- 20 minutes
    
    -- Visual/Audio cues
    beepInterval = {
        initial = 5000, -- 5 seconds
        final = 1000    -- 1 second when < 1 minute left
    },
    
    effects = {
        explosion = 'EXPLOSION_TANKER',
        radius = 50.0,
        damage = true
    }
}

-- Anti-Air System (Federal Reserve)
Config.AntiAir = {
    enabled = true,
    outerWarning = 500.0, -- Warning radius
    innerKill = 200.0,    -- Kill radius
    
    hackable = true,
    hackTime = 60, -- 60 seconds to hack
    
    -- When hacked, roles reverse (APD gets shot, civilians safe)
    reverseOnHack = true,
    
    missile = {
        type = 'homing',
        speed = 100.0,
        damage = 10000 -- Instant kill
    }
}

-- Gold Bar System (Federal Reserve)
Config.GoldBars = {
    weight = 6, -- 6 inventory units per bar
    value = 10000, -- Base value per bar
    
    -- Cannot be stored in aerial vehicles
    aerialStorage = false,
    
    -- Can be stored in houses outside federal rings
    houseStorage = true,
    
    -- Calculation based on online officers
    calculation = function(onlineCops)
        local base = { min = 150, max = 225 }
        local corporals = 0
        local sergeants = 0
        
        -- This would be calculated server-side based on actual ranks
        -- For now, using estimated values
        corporals = math.floor(onlineCops * 0.3) -- ~30% corporals
        sergeants = math.floor(onlineCops * 0.2)  -- ~20% sergeants
        
        local bonus = (corporals * 5) + (sergeants * 7)
        local total = base.min + bonus + math.random(0, base.max - base.min)
        
        return total
    end
}
