{"name": "@webassemblyjs/helper-module-context", "version": "1.7.11", "description": "", "main": "lib/index.js", "module": "esm/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "devDependencies": {"@webassemblyjs/wast-parser": "1.7.11", "mamacro": "^0.0.3"}, "publishConfig": {"access": "public"}, "author": "<PERSON>", "license": "MIT", "gitHead": "4291990bfc4648bc6676091a955d12dc3c7e5909"}