QBShared = QBShared or {}

-- Olympus Altis Life Configuration
QBShared.Olympus = {
    -- Zone System Configuration
    Zones = {
        -- Red Zones (KOS - Kill on Sight)
        RedZones = {
            ['warzone'] = {
                name = 'Warzone Island',
                coords = vector3(-2000.0, 3100.0, 32.0),
                radius = 500.0,
                rules = {
                    kos = true,
                    nlr = false, -- No NLR in warzone
                    rdm = false, -- RDM rules don't apply
                    vehicleStorage = false,
                }
            },
            ['rebel_outpost_1'] = {
                name = 'Rebel Outpost North',
                coords = vector3(-1500.0, 4500.0, 50.0),
                radius = 150.0,
                rules = { kos = true, nlr = true, rdm = false }
            },
            ['rebel_outpost_2'] = {
                name = 'Rebel Outpost South',
                coords = vector3(2000.0, -1500.0, 30.0),
                radius = 150.0,
                rules = { kos = true, nlr = true, rdm = false }
            },
        },
        
        -- Blue Zones (Special Event Rules)
        BlueZones = {
            ['apd_escort'] = {
                name = 'APD Escort Event',
                dynamic = true, -- Moves with escort vehicle
                radius = 200.0,
                rules = {
                    civiliansKillCops = true, -- Civilians can kill cops on sight
                    noExplosives = true, -- No explosives on escort vehicle
                    rdmStillApplies = true, -- RDM rules still apply civ vs civ
                }
            },
            ['pharmaceutical'] = {
                name = 'Pharmaceutical Robbery',
                coords = vector3(3500.0, 3700.0, 35.0),
                radius = 100.0,
                rules = { civiliansKillCops = true, rdmStillApplies = true }
            },
            ['vehicle_yard'] = {
                name = 'Vehicle Yard',
                coords = vector3(2500.0, 4200.0, 40.0),
                radius = 100.0,
                rules = { civiliansKillCops = true, rdmStillApplies = true }
            },
            ['art_gallery'] = {
                name = 'Art Gallery',
                coords = vector3(-1900.0, -600.0, 11.0),
                radius = 100.0,
                rules = { civiliansKillCops = true, rdmStillApplies = true }
            },
        },
        
        -- Illegal Areas (Red crosshatch zones)
        IllegalAreas = {
            ['cocaine_field'] = {
                name = 'Cocaine Field',
                coords = vector3(2400.0, 4800.0, 35.0),
                radius = 100.0,
                type = 'processing',
                drug = 'cocaine'
            },
            ['meth_lab'] = {
                name = 'Meth Lab',
                coords = vector3(1500.0, 6600.0, 25.0),
                radius = 100.0,
                type = 'processing',
                drug = 'meth'
            },
            ['weed_field'] = {
                name = 'Weed Field',
                coords = vector3(2200.0, 5200.0, 65.0),
                radius = 100.0,
                type = 'processing',
                drug = 'weed'
            },
            ['heroin_lab'] = {
                name = 'Heroin Lab',
                coords = vector3(3300.0, 5400.0, 8.0),
                radius = 100.0,
                type = 'processing',
                drug = 'heroin'
            },
        }
    },
    
    -- Federal Events Configuration
    FederalEvents = {
        ['federal_reserve'] = {
            name = 'Federal Reserve',
            coords = vector3(5000.0, -5200.0, 2.0),
            domes = {
                vector3(4980.0, -5180.0, 15.0),
                vector3(5020.0, -5220.0, 15.0)
            },
            requirements = {
                minCops = 5,
                blastingCharge = true,
                boltCutters = true
            },
            rewards = {
                goldBars = { min = 150, max = 425 },
                bonusPerCop = { corporal = 5, sergeant = 7 }
            },
            cooldown = {
                success = 3000, -- 50 minutes
                failure = 2100 -- 35 minutes
            }
        },
        ['blackwater_armory'] = {
            name = 'Blackwater Armory',
            coords = vector3(-2300.0, 3200.0, 32.0),
            requirements = {
                minCops = 5,
                blastingCharge = true,
                boltCutters = true
            },
            rewards = {
                weapons = true,
                equipment = true
            },
            priority = 1 -- Highest priority federal event
        },
        ['altis_penitentiary'] = {
            name = 'Altis Penitentiary',
            coords = vector3(1700.0, 2600.0, 45.0),
            requirements = {
                minCops = 5,
                blastingCharge = true,
                boltCutters = true
            },
            rewards = {
                prisonerRelease = true
            },
            priority = 4 -- Lowest priority
        },
        ['evidence_lockup'] = {
            name = 'Evidence Lockup',
            coords = vector3(400.0, -1600.0, 29.0),
            requirements = {
                minCops = 5,
                blastingCharge = true
            },
            rewards = {
                evidence = true,
                contraband = true
            },
            priority = 3,
            blueZone = true -- Follows blue zone rules
        }
    },
    
    -- Drug Economy System
    DrugEconomy = {
        ['cocaine'] = {
            field = vector3(2400.0, 4800.0, 35.0),
            processor = vector3(2350.0, 4850.0, 35.0),
            dealer = vector3(-1500.0, -300.0, 50.0),
            cartelTax = 'church', -- Which cartel taxes this drug
            basePrice = 4500,
            processingTime = 10, -- seconds per unit
            requiredLicense = false
        },
        ['meth'] = {
            field = vector3(1500.0, 6600.0, 25.0),
            processor = vector3(1450.0, 6650.0, 25.0),
            dealer = vector3(-1400.0, -250.0, 50.0),
            cartelTax = 'alpha_point',
            basePrice = 3800,
            processingTime = 12,
            requiredLicense = false
        },
        ['weed'] = {
            field = vector3(2200.0, 5200.0, 65.0),
            processor = vector3(2150.0, 5250.0, 65.0),
            dealer = vector3(-1600.0, -350.0, 50.0),
            cartelTax = 'church',
            basePrice = 2200,
            processingTime = 8,
            requiredLicense = false
        },
        ['heroin'] = {
            field = vector3(3300.0, 5400.0, 8.0),
            processor = vector3(3250.0, 5450.0, 8.0),
            dealer = vector3(-1300.0, -200.0, 50.0),
            cartelTax = 'alpha_point',
            basePrice = 5200,
            processingTime = 15,
            requiredLicense = false
        },
        ['moonshine'] = {
            field = vector3(1800.0, 3400.0, 120.0),
            processor = vector3(1750.0, 3450.0, 120.0),
            dealer = vector3(-1550.0, -275.0, 50.0),
            cartelTax = 'alpha_point',
            basePrice = 3200,
            processingTime = 20,
            requiredLicense = false
        },
        ['mushroom'] = {
            field = vector3(2800.0, 4400.0, 45.0),
            processor = vector3(2750.0, 4450.0, 45.0),
            dealer = vector3(-1450.0, -325.0, 50.0),
            cartelTax = 'alpha_point',
            basePrice = 2800,
            processingTime = 14,
            requiredLicense = false
        },
        ['acid'] = {
            field = vector3(3600.0, 3900.0, 15.0),
            processor = vector3(3550.0, 3950.0, 15.0),
            dealer = vector3(-1350.0, -225.0, 50.0),
            cartelTax = false, -- No cartel taxes acid
            basePrice = 6500,
            processingTime = 25,
            requiredLicense = false
        }
    },
    
    -- Legal Economy System
    LegalEconomy = {
        ['salt'] = {
            field = vector3(-2000.0, 2300.0, 30.0),
            processor = vector3(-1950.0, 2350.0, 30.0),
            dealer = vector3(100.0, -1900.0, 20.0),
            basePrice = 1200,
            processingTime = 5,
            requiredLicense = true,
            licenseType = 'salt_processing'
        },
        ['sand'] = {
            field = vector3(2700.0, 1500.0, 24.0),
            processor = vector3(2750.0, 1550.0, 24.0),
            dealer = vector3(150.0, -1850.0, 20.0),
            basePrice = 800,
            processingTime = 4,
            requiredLicense = true,
            licenseType = 'sand_processing'
        },
        ['iron'] = {
            field = vector3(2950.0, 2800.0, 40.0),
            processor = vector3(3000.0, 2850.0, 40.0),
            dealer = vector3(200.0, -1800.0, 20.0),
            basePrice = 1800,
            processingTime = 8,
            requiredLicense = true,
            licenseType = 'iron_processing'
        },
        ['copper'] = {
            field = vector3(-1400.0, 4700.0, 55.0),
            processor = vector3(-1350.0, 4750.0, 55.0),
            dealer = vector3(250.0, -1750.0, 20.0),
            basePrice = 1600,
            processingTime = 7,
            requiredLicense = true,
            licenseType = 'copper_processing'
        },
        ['diamond'] = {
            field = vector3(2900.0, 4300.0, 50.0),
            processor = vector3(2950.0, 4350.0, 50.0),
            dealer = vector3(300.0, -1700.0, 20.0),
            basePrice = 4200,
            processingTime = 15,
            requiredLicense = true,
            licenseType = 'diamond_processing'
        },
        ['oil'] = {
            field = vector3(600.0, 2900.0, 40.0),
            processor = vector3(650.0, 2950.0, 40.0),
            dealer = vector3(350.0, -1650.0, 20.0),
            basePrice = 2200,
            processingTime = 10,
            requiredLicense = true,
            licenseType = 'oil_processing'
        }
    }
}
