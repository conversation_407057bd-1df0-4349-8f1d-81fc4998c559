Config = {}

-- Cartel System Configuration (Olympus Style)
Config.Cartels = {
    ['og_arms'] = {
        name = 'OG Arms',
        label = 'OG Arms Cartel',
        coords = vector3(-2047.0, 3132.0, 32.0),
        radius = 95.0,
        captureTime = 300, -- 5 minutes in seconds
        contestedMultiplier = 0.5, -- 50% speed when contested
        speedBonus = {
            twoPlayers = 1.375, -- 37.5% bonus with 2 players
            threePlayers = 1.75  -- 75% total bonus with 3+ players
        },
        benefits = {
            type = 'arms_dealer',
            discount = 15, -- 15% discount at rebel outposts
            taxRate = 15   -- 15% tax on purchases
        },
        permanent = true, -- Always at this location
        blip = {
            sprite = 437,
            color = 1,
            scale = 1.0
        }
    },
    
    ['church'] = {
        name = 'Church',
        label = 'Church Cartel',
        coords = vector3(-1950.0, 3200.0, 32.0),
        radius = 95.0,
        captureTime = 300,
        contestedMultiplier = 0.5,
        speedBonus = {
            twoPlayers = 1.375,
            threePlayers = 1.75
        },
        benefits = {
            type = 'drug_taxation',
            drugs = { 'cocaine', 'meth', 'weed' }, -- Rotates with alpha point
            taxRate = 15
        },
        rotates = true,
        alternateLocation = vector3(-2100.0, 3050.0, 32.0), -- Alpha Point location
        blip = {
            sprite = 437,
            color = 2,
            scale = 1.0
        }
    },
    
    ['alpha_point'] = {
        name = 'Alpha Point',
        label = 'Alpha Point Cartel',
        coords = vector3(-2100.0, 3050.0, 32.0),
        radius = 95.0,
        captureTime = 300,
        contestedMultiplier = 0.5,
        speedBonus = {
            twoPlayers = 1.375,
            threePlayers = 1.75
        },
        benefits = {
            type = 'drug_taxation',
            drugs = { 'moonshine', 'mushroom', 'heroin' }, -- Rotates with church
            taxRate = 15
        },
        rotates = true,
        alternateLocation = vector3(-1950.0, 3200.0, 32.0), -- Church location
        blip = {
            sprite = 437,
            color = 3,
            scale = 1.0
        }
    }
}

-- Warzone Configuration
Config.Warzone = {
    coords = vector3(-2000.0, 3100.0, 32.0),
    radius = 500.0,
    rules = {
        kos = true,           -- Kill on sight
        nlr = false,          -- No new life rule
        rdm = false,          -- RDM rules don't apply
        vehicleStorage = false -- Cannot store vehicles
    },
    blip = {
        sprite = 84,
        color = 1,
        scale = 1.5,
        alpha = 128
    }
}

-- Passive Income Configuration
Config.PassiveIncome = {
    enabled = true,
    payoutInterval = 600000, -- 10 minutes in milliseconds
    baseAmount = 30000,      -- Base amount per cycle
    calculation = function(playerCount)
        -- Olympus formula: (players / 100) * 30000
        return math.floor((playerCount / 100) * Config.PassiveIncome.baseAmount)
    end
}

-- Gang Requirements
Config.GangRequirements = {
    minWeaponCaliberForCapture = '5.56', -- Must have 5.56 caliber or greater
    maxCaptureDistance = 95,             -- Must be within 95 meters
    cannotCaptureFromVehicle = true,     -- Cannot capture from inside vehicle
    requiredWeapons = {
        'weapon_carbinerifle',    -- 5.56
        'weapon_assaultrifle',    -- 7.62
        'weapon_specialcarbine',  -- 5.56
        'weapon_bullpuprifle',    -- 5.56
        'weapon_advancedrifle',   -- 5.56
        'weapon_sniperrifle',     -- 7.62
        'weapon_heavysniper',     -- .50
        'weapon_marksmanrifle',   -- 7.62
        'weapon_combatmg',        -- 7.62
        'weapon_mg'               -- 7.62
    }
}

-- Cartel Perks
Config.CartelPerks = {
    ['tier1'] = {
        name = 'Tier 1 Gang Perks',
        requirements = {
            gangLevel = 1
        },
        benefits = {
            magRefillCost = 5000,
            magRefillCooldown = 600, -- 10 minutes
            excludedMags = { 'weapon_snspistol_mag', 'weapon_heavysniper_mag' }
        }
    },
    
    ['tier2'] = {
        name = 'Tier 2 Gang Perks',
        requirements = {
            gangLevel = 2
        },
        benefits = {
            magRefillCost = 3000,
            magRefillCooldown = 180, -- 3 minutes
            excludedMags = { 'weapon_snspistol_mag', 'weapon_heavysniper_mag' }
        }
    }
}

-- APD Notification System
Config.APDNotification = {
    enabled = true,
    message = "Gang activity detected at %s cartel",
    dispatch = true,
    blipDuration = 300000 -- 5 minutes
}

-- Cartel Rotation System
Config.CartelRotation = {
    enabled = true,
    rotationInterval = 86400000, -- 24 hours in milliseconds
    rotatingCartels = { 'church', 'alpha_point' },
    notification = {
        enabled = true,
        message = "Cartel locations have rotated!",
        duration = 10000
    }
}
