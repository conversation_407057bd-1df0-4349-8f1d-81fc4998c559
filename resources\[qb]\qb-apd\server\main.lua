local QBCore = exports['qb-core']:GetCoreObject()

-- APD Data Storage
local APDData = {
    onDutyOfficers = {},
    activeWaves = {},
    federalEvents = {},
    processingQueue = {},
    lethalAuthorizations = {}
}

-- Initialize
CreateThread(function()
    -- Load APD data from database
    LoadAPDData()
end)

function LoadAPDData()
    -- Load any persistent APD data
    print('[QB-APD] APD System initialized')
end

-- Duty Management
RegisterNetEvent('qb-apd:server:UpdateDutyStatus', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player then return end
    
    if Player.PlayerData.job.name == 'police' then
        if Player.PlayerData.job.onduty then
            APDData.onDutyOfficers[src] = {
                citizenid = Player.PlayerData.citizenid,
                name = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
                rank = Player.PlayerData.job.grade.level,
                callsign = GenerateCallsign(Player.PlayerData.job.grade.level)
            }
            
            -- Notify all officers of new officer on duty
            TriggerClientEvent('qb-apd:client:OfficerOnDuty', -1, APDData.onDutyOfficers[src])
        else
            APDData.onDutyOfficers[src] = nil
            TriggerClientEvent('qb-apd:client:OfficerOffDuty', -1, src)
        end
    end
end)

function GenerateCallsign(rank)
    local callsigns = {
        [0] = 'D-', -- Deputy
        [1] = 'PO-', -- Patrol Officer
        [2] = 'CPL-', -- Corporal
        [3] = 'SGT-', -- Sergeant
        [4] = 'LT-', -- Lieutenant
        [5] = 'CPT-', -- Captain
        [6] = 'DC-', -- Deputy Chief
        [7] = 'C-' -- Chief
    }
    
    local prefix = callsigns[rank] or 'UNK-'
    local number = math.random(100, 999)
    return prefix .. number
end

-- Code 3 Engagement System
RegisterNetEvent('qb-apd:server:Code3Engagement', function(coords)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    
    -- Get all players within hearing distance of Code 3
    local players = QBCore.Functions.GetPlayers()
    for _, playerId in pairs(players) do
        local targetPlayer = QBCore.Functions.GetPlayer(playerId)
        if targetPlayer then
            local targetCoords = GetEntityCoords(GetPlayerPed(playerId))
            local distance = #(coords - targetCoords)
            
            -- If within 500 meters and not APD, they are engaged
            if distance <= 500.0 and targetPlayer.PlayerData.job.name ~= 'police' then
                TriggerClientEvent('qb-apd:client:EngagedByCode3', playerId, src)
            end
        end
    end
end)

-- Wave System Management
RegisterNetEvent('qb-apd:server:StartWave', function(location, waveNumber)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if not Player or Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    
    -- Check if officer has proper rank to start wave
    if Player.PlayerData.job.grade.level < 1 then -- Must be PO+
        TriggerClientEvent('QBCore:Notify', src, 'You must be Patrol Officer+ to start a wave', 'error')
        return
    end
    
    -- Initialize wave data
    if not APDData.activeWaves[location] then
        APDData.activeWaves[location] = {
            currentWave = 1,
            officers = {},
            startTime = os.time()
        }
    else
        APDData.activeWaves[location].currentWave = APDData.activeWaves[location].currentWave + 1
    end
    
    local wave = APDData.activeWaves[location]
    wave.officers[src] = true
    
    -- Notify officer of wave assignment
    TriggerClientEvent('qb-apd:client:JoinWave', src, wave.currentWave, location)
    
    -- Notify other officers
    for officerId, _ in pairs(APDData.onDutyOfficers) do
        if officerId ~= src then
            TriggerClientEvent('QBCore:Notify', officerId, 'Wave ' .. wave.currentWave .. ' started at ' .. location, 'primary')
        end
    end
end)

RegisterNetEvent('qb-apd:server:CompleteWave', function(location)
    local src = source
    
    if APDData.activeWaves[location] then
        -- Remove officer from wave
        APDData.activeWaves[location].officers[src] = nil
        
        -- Check if wave is complete (no officers left)
        local officersLeft = 0
        for _, _ in pairs(APDData.activeWaves[location].officers) do
            officersLeft = officersLeft + 1
        end
        
        if officersLeft == 0 then
            -- Wave complete
            APDData.activeWaves[location] = nil
            
            -- Notify all officers
            for officerId, _ in pairs(APDData.onDutyOfficers) do
                TriggerClientEvent('qb-apd:client:WaveComplete', officerId)
            end
        end
    end
end)

-- Federal Event Management
RegisterNetEvent('qb-apd:server:StartFederalEvent', function(eventType, location)
    local src = source
    
    -- Check minimum officers requirement
    local onDutyCount = 0
    for _, _ in pairs(APDData.onDutyOfficers) do
        onDutyCount = onDutyCount + 1
    end
    
    if onDutyCount < 5 then
        TriggerClientEvent('QBCore:Notify', src, 'Minimum 5 officers required for federal events', 'error')
        return
    end
    
    -- Initialize federal event
    APDData.federalEvents[eventType] = {
        location = location,
        startTime = os.time(),
        bombPlanted = false,
        active = true
    }
    
    -- Notify all officers of federal event
    for officerId, _ in pairs(APDData.onDutyOfficers) do
        TriggerClientEvent('qb-apd:client:FederalEventStarted', officerId, eventType, location)
    end
    
    -- Send priority dispatch
    local priority = Config.APD.FederalEvents.priority
    local eventPriority = 4
    for i, event in ipairs(priority) do
        if event == eventType then
            eventPriority = i
            break
        end
    end
    
    TriggerClientEvent('qb-apd:client:PriorityDispatch', -1, {
        type = 'federal_event',
        event = eventType,
        location = location,
        priority = eventPriority,
        message = 'Federal Event: ' .. eventType .. ' at ' .. location
    })
end)

-- Processing System
RegisterNetEvent('qb-apd:server:StartProcessing', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    if Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    
    -- Add to processing queue
    APDData.processingQueue[targetId] = {
        officer = src,
        startTime = os.time(),
        suspect = targetId,
        charges = {},
        evidence = {}
    }
    
    -- Notify both players
    TriggerClientEvent('qb-apd:client:StartProcessing', src, targetId)
    TriggerClientEvent('qb-apd:client:BeingProcessed', targetId, src)
end)

RegisterNetEvent('qb-apd:server:AddCharge', function(targetId, charge, fine)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player or Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    if not APDData.processingQueue[targetId] then return end
    
    -- Add charge to processing queue
    table.insert(APDData.processingQueue[targetId].charges, {
        charge = charge,
        fine = fine,
        officer = src,
        time = os.time()
    })
    
    TriggerClientEvent('QBCore:Notify', src, 'Charge added: ' .. charge .. ' ($' .. fine .. ')', 'success')
end)

RegisterNetEvent('qb-apd:server:PardonSuspect', function(targetId, reason)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    if Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    
    -- Check if officer has authority to pardon
    if Player.PlayerData.job.grade.level < 1 and reason ~= 'time_limit_exceeded' then
        -- Deputies can only pardon if no higher ranking officers online
        local higherRankOnline = false
        for _, officerData in pairs(APDData.onDutyOfficers) do
            if officerData.rank > 0 then
                higherRankOnline = true
                break
            end
        end
        
        if higherRankOnline then
            TriggerClientEvent('QBCore:Notify', src, 'You need approval from a higher ranking officer', 'error')
            return
        end
    end
    
    -- Clear processing queue
    APDData.processingQueue[targetId] = nil
    
    -- Notify both players
    TriggerClientEvent('QBCore:Notify', src, 'Suspect pardoned: ' .. reason, 'success')
    TriggerClientEvent('QBCore:Notify', targetId, 'You have been pardoned: ' .. reason, 'success')
    
    -- Log the pardon
    TriggerEvent('qb-log:server:CreateLog', 'apd', 'Pardon Issued', 'green', 
        Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname .. 
        ' pardoned ' .. Target.PlayerData.charinfo.firstname .. ' ' .. Target.PlayerData.charinfo.lastname .. 
        ' - Reason: ' .. reason)
end)

-- Lethal Authorization System
RegisterNetEvent('qb-apd:server:AuthorizeLethal', function(targetId, reason)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local Target = QBCore.Functions.GetPlayer(targetId)
    
    if not Player or not Target then return end
    if Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then return end
    
    -- Check if officer has authority to authorize lethal
    if Player.PlayerData.job.grade.level < 2 then -- Must be Corporal+
        TriggerClientEvent('QBCore:Notify', src, 'You must be Corporal+ to authorize lethal force', 'error')
        return
    end
    
    -- Authorize lethal for target
    APDData.lethalAuthorizations[targetId] = {
        authorizedBy = src,
        reason = reason,
        time = os.time()
    }
    
    TriggerClientEvent('qb-apd:client:AuthorizeLethal', targetId, reason)
    TriggerClientEvent('QBCore:Notify', src, 'Lethal force authorized for officer', 'success')
end)

-- Get online officer count
QBCore.Functions.CreateCallback('qb-apd:server:GetOnlineOfficers', function(source, cb)
    local count = 0
    for _, _ in pairs(APDData.onDutyOfficers) do
        count = count + 1
    end
    cb(count, APDData.onDutyOfficers)
end)

-- Player disconnect cleanup
AddEventHandler('playerDropped', function()
    local src = source
    
    -- Clean up APD data
    APDData.onDutyOfficers[src] = nil
    APDData.processingQueue[src] = nil
    APDData.lethalAuthorizations[src] = nil
    
    -- Clean up from active waves
    for location, waveData in pairs(APDData.activeWaves) do
        if waveData.officers[src] then
            waveData.officers[src] = nil
        end
    end
end)
