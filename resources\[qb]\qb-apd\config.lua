Config = {}

-- APD Configuration based on Olympus Altis Life
Config.APD = {
    -- Rank System (matches Olympus exactly)
    Ranks = {
        [0] = {
            name = 'Deputy',
            label = 'Deputy',
            minTime = 1800, -- 1800 minutes on server
            canLethal = false,
            canInterview = false,
            canAuthorize = false,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight' },
                vehicles = { 'police', 'police2' },
                clothing = { 'deputy_uniform' }
            }
        },
        [1] = {
            name = 'Patrol Officer',
            label = 'Patrol Officer',
            minTime = 1200, -- 1200 minutes
            timeInGrade = 7, -- 7 days
            canLethal = false,
            canInterview = false,
            canAuthorize = false,
            canPatrolIllegal = true,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle' },
                vehicles = { 'police', 'police2', 'police3' },
                clothing = { 'po_uniform' }
            }
        },
        [2] = {
            name = 'Corporal',
            label = 'Corporal',
            minTime = 10000, -- 10000 minutes
            timeInGrade = 30, -- 30 days
            canLethal = true, -- First lethal loading officer
            canInterview = false,
            canAuthorize = true,
            canFTO = true, -- Can apply for Field Training Officer
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb' },
                clothing = { 'corporal_uniform' }
            }
        },
        [3] = {
            name = 'Sergeant',
            label = 'Sergeant',
            canLethal = true,
            canInterview = true, -- Interviews new applicants
            canAuthorize = true,
            canIssueWarrants = true,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle', 'weapon_sniperrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb', 'polmav' },
                clothing = { 'sergeant_uniform' }
            }
        },
        [4] = {
            name = 'Lieutenant',
            label = 'Lieutenant',
            canLethal = true,
            canInterview = true,
            canAcceptApps = true, -- Accepts applications
            canAuthorize = true,
            canIssueWarrants = true,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle', 'weapon_sniperrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb', 'polmav', 'riot' },
                clothing = { 'lieutenant_uniform' }
            }
        },
        [5] = {
            name = 'Captain',
            label = 'Captain',
            sectionHead = true, -- Responsible for a section
            canLethal = true,
            canInterview = true,
            canAcceptApps = true,
            canAuthorize = true,
            canIssueWarrants = true,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle', 'weapon_sniperrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb', 'polmav', 'riot', 'fbi2' },
                clothing = { 'captain_uniform' }
            }
        },
        [6] = {
            name = 'Deputy Chief',
            label = 'Deputy Chief of Police',
            advisor = true, -- Adviser to Chief
            canLethal = true,
            canInterview = true,
            canAcceptApps = true,
            canAuthorize = true,
            canIssueWarrants = true,
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle', 'weapon_sniperrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb', 'polmav', 'riot', 'fbi2' },
                clothing = { 'deputy_chief_uniform' }
            }
        },
        [7] = {
            name = 'Chief',
            label = 'Chief of Police',
            finalSay = true, -- Final say on all APD matters
            canLethal = true,
            canInterview = true,
            canAcceptApps = true,
            canAuthorize = true,
            canIssueWarrants = true,
            canOverrideHandbook = true, -- Executive decision power
            equipment = {
                weapons = { 'weapon_stungun', 'weapon_pistol', 'weapon_nightstick', 'weapon_flashlight', 'weapon_carbinerifle', 'weapon_assaultrifle', 'weapon_sniperrifle' },
                vehicles = { 'police', 'police2', 'police3', 'police4', 'policeb', 'polmav', 'riot', 'fbi2' },
                clothing = { 'chief_uniform' }
            }
        }
    },
    
    -- Use of Force Rules
    UseOfForce = {
        -- Non-lethal is default
        nonLethalDefault = true,
        
        -- Lethal force authorization conditions
        lethalConditions = {
            temporaryAuth = {
                'warzone', 'cartels', 'gang_base', 'rebel_outpost', -- In red zones
                'server_restart_5min', -- 5 minutes before restart
                'deputy_po_authorized', -- Deputies/POs authorized by Senior APD
                'tasers_inadequate', -- Tasers inadequate for situation
                'roof_no_access', -- Suspect on inaccessible roof
                'fired_upon', -- Fired upon without engagement
                'armed_vehicle_gunner', -- Suspect in armed vehicle gunner seat
                'trial_by_combat' -- SGT+ trial by combat
            },
            permanentAuth = {
                'two_failed_attempts', -- After 2 failed non-lethal attempts
                'officer_hostage', -- Officer taken hostage or tased
                'three_to_one', -- Outnumbered 3:1 ratio
                'ghosthawk_guns_hot', -- Ghosthawk/Armed Plane guns hot
                'server_restart_active' -- Lethal remains active through restart
            }
        },
        
        -- Vehicle shooting rules
        vehicleShooting = {
            groundVehicles = true, -- Can shoot at evading ground vehicles
            armedAerial = true, -- Can destroy armed aerial vehicles
            slingLoad = true, -- Can destroy sling-loaded armed vehicles
            anyMeansNecessary = true -- Any means necessary for stolen APD aircraft
        }
    },
    
    -- Processing Rules
    Processing = {
        maxTime = 15, -- 15 minutes maximum processing time
        exceptions = {
            fiveOrMore = true, -- Exception for 5+ suspects
            moreThankOfficers = true, -- More suspects than officers
            rpSituation = true -- RP situation extends time
        },
        
        -- L.I.S.T. Protocol
        protocol = {
            'licenses', -- Check licenses
            'inventory', -- Search inventory (with probable cause)
            'seize', -- Seize illegal items
            'ticket' -- Issue ticket
        },
        
        -- Probable cause examples
        probableCause = {
            'illegal_area', -- In or leaving illegal area
            'illegal_item', -- Visible illegal item/weapon
            'illegal_activity', -- Witnessed illegal activity
            'wanted_individual', -- Wanted person
            'restricted_weapon', -- Carrying restricted weapon
            'gang_vehicle_situation' -- Gang member with gang vehicle present
        }
    },
    
    -- Wave System for Illegal Areas
    WaveSystem = {
        enabled = true,
        rules = {
            respondInWaves = true,
            waveComplete = 'all_officers_dead_or_neutralized',
            firstWaveBackup = true, -- Can call backup on first wave
            subsequentWaves = 'start_at_hq', -- Must start at HQ
            leaveToHQ = true, -- Must reach HQ before next wave
            sameWaveReentry = true -- Can re-enter if part of same wave
        },
        
        exceptions = {
            activeBank = 'five_or_less_officers', -- No waves if 5 or less officers at bank
            civiliansFlee = 'waves_no_longer_required', -- No waves if civilians leave area
            areaSecured = 'all_officers_return' -- All can return when secured
        }
    },
    
    -- Federal Event Rules
    FederalEvents = {
        priority = { 'blackwater', 'federal_reserve', 'evidence_lockup', 'jail' },
        
        rules = {
            noRP = true, -- No RP required to engage participants
            waveSystem = true,
            mandatoryResponse = true, -- Must respond (except if 4 or less officers)
            bombPlanted = 'takes_priority', -- Event with bomb planted takes priority
        },
        
        equipment = {
            deputyPOLethals = 'ten_or_less_officers', -- Deputies/POs can use lethals if 10 or less officers
            type115Corporals = 'extreme_disadvantage', -- Type 115s for Corporals in extreme cases
            patrolOfficerVests = 'extreme_disadvantage', -- Tier 3 vests for POs
            sparMK20Deputies = 'extreme_disadvantage' -- Spar and MK20 for Deputies
        }
    }
}

-- Locations
Config.Locations = {
    -- APD Headquarters
    HQs = {
        ['kavala'] = {
            name = 'Kavala HQ',
            coords = vector3(442.5, -982.0, 30.7),
            spawn = vector3(442.5, -1018.0, 28.6),
            garage = vector3(454.6, -1017.4, 28.4),
            armory = vector3(451.7, -980.1, 30.7),
            processing = vector3(441.1, -978.3, 30.7)
        },
        ['pyrgos'] = {
            name = 'Pyrgos HQ',
            coords = vector3(-448.0, 6012.0, 31.7),
            spawn = vector3(-448.0, 6025.0, 31.7),
            garage = vector3(-455.0, 6030.0, 31.7),
            armory = vector3(-440.0, 6015.0, 31.7),
            processing = vector3(-445.0, 6008.0, 31.7)
        },
        ['athira'] = {
            name = 'Athira HQ',
            coords = vector3(1850.0, 3690.0, 34.3),
            spawn = vector3(1850.0, 3705.0, 34.3),
            garage = vector3(1860.0, 3710.0, 34.3),
            armory = vector3(1845.0, 3695.0, 34.3),
            processing = vector3(1840.0, 3685.0, 34.3)
        }
    },
    
    -- Checkpoints
    Checkpoints = {
        rules = {
            minDistance = 1000, -- 1km from red zones
            minOfficers = 3, -- PO+ and 2 other officers
            vehiclesRequired = true, -- APD vehicles with lights
            searchConsent = true -- Need consent or probable cause
        }
    }
}
