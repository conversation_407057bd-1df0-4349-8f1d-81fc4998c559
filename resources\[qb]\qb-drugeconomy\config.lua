Config = {}

-- Drug Economy Configuration (Olympus Style)
Config.Drugs = {
    ['cocaine'] = {
        name = 'Cocaine',
        rawItem = 'coca_leaf',
        processedItem = 'cocaine',
        
        field = {
            coords = vector3(2400.0, 4800.0, 35.0),
            radius = 100.0,
            harvestTime = 5, -- seconds per harvest
            harvestAmount = { min = 1, max = 3 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(2350.0, 4850.0, 35.0),
            radius = 50.0,
            processTime = 10, -- seconds per unit
            processAmount = 1, -- 1 processed per 1 raw
            requiredItems = { 'coca_leaf' }
        },
        
        dealer = {
            coords = vector3(-1500.0, -300.0, 50.0),
            radius = 25.0,
            basePrice = 4500,
            priceVariation = 0.1 -- ±10% price variation
        },
        
        cartelTax = 'church', -- Which cartel taxes this drug
        taxRate = 0.15, -- 15% tax rate
        
        illegal = true,
        probableCause = true, -- Provides probable cause for search
        
        blip = {
            field = { sprite = 140, color = 1, scale = 0.8 },
            processor = { sprite = 478, color = 1, scale = 0.8 },
            dealer = { sprite = 500, color = 1, scale = 0.8 }
        }
    },
    
    ['meth'] = {
        name = 'Crystal Meth',
        rawItem = 'ephedrine',
        processedItem = 'meth',
        
        field = {
            coords = vector3(1500.0, 6600.0, 25.0),
            radius = 100.0,
            harvestTime = 6,
            harvestAmount = { min = 1, max = 2 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(1450.0, 6650.0, 25.0),
            radius = 50.0,
            processTime = 12,
            processAmount = 1,
            requiredItems = { 'ephedrine' }
        },
        
        dealer = {
            coords = vector3(-1400.0, -250.0, 50.0),
            radius = 25.0,
            basePrice = 3800,
            priceVariation = 0.1
        },
        
        cartelTax = 'alpha_point',
        taxRate = 0.15,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 2, scale = 0.8 },
            processor = { sprite = 478, color = 2, scale = 0.8 },
            dealer = { sprite = 500, color = 2, scale = 0.8 }
        }
    },
    
    ['weed'] = {
        name = 'Marijuana',
        rawItem = 'weed_plant',
        processedItem = 'weed',
        
        field = {
            coords = vector3(2200.0, 5200.0, 65.0),
            radius = 100.0,
            harvestTime = 4,
            harvestAmount = { min = 2, max = 4 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(2150.0, 5250.0, 65.0),
            radius = 50.0,
            processTime = 8,
            processAmount = 1,
            requiredItems = { 'weed_plant' }
        },
        
        dealer = {
            coords = vector3(-1600.0, -350.0, 50.0),
            radius = 25.0,
            basePrice = 2200,
            priceVariation = 0.1
        },
        
        cartelTax = 'church',
        taxRate = 0.15,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 2, scale = 0.8 },
            processor = { sprite = 478, color = 2, scale = 0.8 },
            dealer = { sprite = 500, color = 2, scale = 0.8 }
        }
    },
    
    ['heroin'] = {
        name = 'Heroin',
        rawItem = 'opium_poppy',
        processedItem = 'heroin',
        
        field = {
            coords = vector3(3300.0, 5400.0, 8.0),
            radius = 100.0,
            harvestTime = 8,
            harvestAmount = { min = 1, max = 2 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(3250.0, 5450.0, 8.0),
            radius = 50.0,
            processTime = 15,
            processAmount = 1,
            requiredItems = { 'opium_poppy' }
        },
        
        dealer = {
            coords = vector3(-1300.0, -200.0, 50.0),
            radius = 25.0,
            basePrice = 5200,
            priceVariation = 0.1
        },
        
        cartelTax = 'alpha_point',
        taxRate = 0.15,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 1, scale = 0.8 },
            processor = { sprite = 478, color = 1, scale = 0.8 },
            dealer = { sprite = 500, color = 1, scale = 0.8 }
        }
    },
    
    ['moonshine'] = {
        name = 'Moonshine',
        rawItem = 'corn',
        processedItem = 'moonshine',
        
        field = {
            coords = vector3(1800.0, 3400.0, 120.0),
            radius = 100.0,
            harvestTime = 10,
            harvestAmount = { min = 1, max = 3 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(1750.0, 3450.0, 120.0),
            radius = 50.0,
            processTime = 20,
            processAmount = 1,
            requiredItems = { 'corn' }
        },
        
        dealer = {
            coords = vector3(-1550.0, -275.0, 50.0),
            radius = 25.0,
            basePrice = 3200,
            priceVariation = 0.1
        },
        
        cartelTax = 'alpha_point',
        taxRate = 0.15,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 3, scale = 0.8 },
            processor = { sprite = 478, color = 3, scale = 0.8 },
            dealer = { sprite = 500, color = 3, scale = 0.8 }
        }
    },
    
    ['mushroom'] = {
        name = 'Psychedelic Mushrooms',
        rawItem = 'mushroom_spores',
        processedItem = 'mushroom',
        
        field = {
            coords = vector3(2800.0, 4400.0, 45.0),
            radius = 100.0,
            harvestTime = 7,
            harvestAmount = { min = 1, max = 3 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(2750.0, 4450.0, 45.0),
            radius = 50.0,
            processTime = 14,
            processAmount = 1,
            requiredItems = { 'mushroom_spores' }
        },
        
        dealer = {
            coords = vector3(-1450.0, -325.0, 50.0),
            radius = 25.0,
            basePrice = 2800,
            priceVariation = 0.1
        },
        
        cartelTax = 'alpha_point',
        taxRate = 0.15,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 5, scale = 0.8 },
            processor = { sprite = 478, color = 5, scale = 0.8 },
            dealer = { sprite = 500, color = 5, scale = 0.8 }
        }
    },
    
    ['acid'] = {
        name = 'LSD (Acid)',
        rawItem = 'ergot_fungus',
        processedItem = 'acid',
        
        field = {
            coords = vector3(3600.0, 3900.0, 15.0),
            radius = 100.0,
            harvestTime = 12,
            harvestAmount = { min = 1, max = 2 },
            requiredTool = nil
        },
        
        processor = {
            coords = vector3(3550.0, 3950.0, 15.0),
            radius = 50.0,
            processTime = 25,
            processAmount = 1,
            requiredItems = { 'ergot_fungus' }
        },
        
        dealer = {
            coords = vector3(-1350.0, -225.0, 50.0),
            radius = 25.0,
            basePrice = 6500,
            priceVariation = 0.1
        },
        
        cartelTax = false, -- Acid cannot be taxed by cartels
        taxRate = 0,
        
        illegal = true,
        probableCause = true,
        
        blip = {
            field = { sprite = 140, color = 7, scale = 0.8 },
            processor = { sprite = 478, color = 7, scale = 0.8 },
            dealer = { sprite = 500, color = 7, scale = 0.8 }
        }
    }
}

-- Special Illegal Activities
Config.SpecialActivities = {
    ['turtle_poaching'] = {
        name = 'Turtle Poaching',
        locations = {
            vector3(4500.0, 4500.0, 1.0),
            vector3(4200.0, 4800.0, 1.0),
            vector3(3800.0, 4600.0, 1.0)
        },
        harvestTime = 15,
        harvestAmount = { min = 1, max = 2 },
        requiredTool = 'diving_gear',
        processedItem = 'turtle_meat',
        basePrice = 3500,
        illegal = true
    },
    
    ['kidney_trade'] = {
        name = 'Kidney Harvesting',
        processor = {
            coords = vector3(-800.0, -1200.0, 5.0),
            radius = 50.0
        },
        requiredItems = { 'kidney' }, -- Obtained from downed players
        processedItem = 'processed_kidney',
        basePrice = 8000,
        illegal = true,
        probableCause = true
    }
}

-- Cartel Taxation System
Config.CartelTaxation = {
    enabled = true,
    taxRate = 0.15, -- 15% default tax rate
    
    -- Which cartels tax which drugs
    taxMapping = {
        ['church'] = { 'cocaine', 'meth', 'weed' },
        ['alpha_point'] = { 'moonshine', 'mushroom', 'heroin' },
        ['og_arms'] = {} -- Arms cartel doesn't tax drugs
    },
    
    -- Rotation system (church and alpha_point swap)
    rotation = {
        enabled = true,
        interval = 86400000, -- 24 hours
        cartels = { 'church', 'alpha_point' }
    }
}

-- Price Fluctuation System
Config.PriceFluctuation = {
    enabled = true,
    updateInterval = 300000, -- 5 minutes
    factors = {
        supply = 0.1,    -- Supply affects price by ±10%
        demand = 0.1,    -- Demand affects price by ±10%
        random = 0.05    -- Random variation ±5%
    },
    
    -- Server population affects prices
    populationMultiplier = {
        low = { threshold = 20, multiplier = 0.8 },    -- <20 players: 80% price
        medium = { threshold = 50, multiplier = 1.0 }, -- 20-50 players: 100% price
        high = { threshold = 100, multiplier = 1.2 }   -- >50 players: 120% price
    }
}

-- Gang Base Processing Bonus
Config.GangBaseBonus = {
    enabled = true,
    processingBonus = 0.2, -- 20% faster processing at gang base
    qualityBonus = 0.1     -- 10% higher quality (price)
}
