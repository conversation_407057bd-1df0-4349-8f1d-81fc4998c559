Config = {}

-- Gang System Configuration (Olympus Style)
Config.Gangs = {
    maxGangs = 20,        -- Maximum number of gangs on server
    maxMembers = 15,      -- Maximum members per gang
    creationCost = 100000, -- Cost to create a gang
    
    -- Gang creation requirements
    requirements = {
        minLevel = 5,     -- Minimum player level
        minPlayTime = 72, -- Minimum hours played
        cleanRecord = true -- No recent bans
    },
    
    -- Gang ranks and permissions
    ranks = {
        [0] = {
            name = 'Recruit',
            permissions = { 'basic' }
        },
        [1] = {
            name = 'Member',
            permissions = { 'basic', 'invite' }
        },
        [2] = {
            name = 'Lieutenant',
            permissions = { 'basic', 'invite', 'kick', 'promote', 'demote' }
        },
        [3] = {
            name = 'Leader',
            permissions = { 'all' },
            isboss = true
        }
    }
}

-- Gang Base System
Config.GangBase = {
    coords = vector3(-1100.0, 4900.0, 220.0),
    radius = 150.0,
    
    -- Capture mechanics
    capture = {
        captureTime = 600, -- 10 minutes
        contestedMultiplier = 0.5,
        requiredWeapons = {
            'weapon_carbinerifle',
            'weapon_assaultrifle',
            'weapon_specialcarbine',
            'weapon_bullpuprifle',
            'weapon_advancedrifle'
        }
    },
    
    -- Benefits of owning gang base
    benefits = {
        processingBonus = 0.2, -- 20% faster drug processing
        storageAccess = true,
        vehicleSpawn = true,
        respawnPoint = true
    },
    
    -- Skirmish system
    skirmish = {
        enabled = true,
        frequency = 3600000, -- Every hour
        duration = 1800000,  -- 30 minutes
        rewards = {
            winner = 50000,
            participation = 10000
        }
    },
    
    blip = {
        sprite = 84,
        color = 1,
        scale = 1.2
    }
}

-- Gang Shed System
Config.GangShed = {
    locations = {
        vector3(-800.0, 5400.0, 34.0),
        vector3(2400.0, 4200.0, 40.0),
        vector3(-2200.0, 4300.0, 50.0),
        vector3(1800.0, 3600.0, 35.0),
        vector3(-1500.0, 2800.0, 55.0)
    },
    
    purchaseCost = 250000, -- Cost to purchase a shed
    
    benefits = {
        vehicleStorage = 10, -- Can store 10 vehicles
        itemStorage = 500,   -- 500 weight units
        drugProcessing = true,
        weaponStorage = true
    },
    
    -- Raid mechanics
    raid = {
        enabled = true,
        raidTime = 300, -- 5 minutes to raid
        requiredItems = { 'boltcutters', 'lockpick' },
        successChance = 0.7, -- 70% success rate
        cooldown = 86400000 -- 24 hour cooldown
    }
}

-- Gang Wars System
Config.GangWars = {
    enabled = true,
    
    -- War declaration
    declaration = {
        cost = 50000,        -- Cost to declare war
        cooldown = 86400000, -- 24 hours between wars
        maxWars = 3,         -- Maximum simultaneous wars
        duration = 7200000   -- 2 hours maximum duration
    },
    
    -- War rules
    rules = {
        redNameSystem = true,    -- Enemy names appear red
        noEngagement = false,    -- Can shoot on sight
        explosivesAllowed = true, -- Can use explosives
        restraintKilling = false, -- Cannot kill restrained enemies
        vehicleRamming = true    -- Can ram enemy vehicles
    },
    
    -- War zones (where wars can take place)
    zones = {
        ['downtown'] = {
            coords = vector3(-200.0, -800.0, 30.0),
            radius = 200.0
        },
        ['industrial'] = {
            coords = vector3(1200.0, -3000.0, 5.0),
            radius = 250.0
        },
        ['docks'] = {
            coords = vector3(-1600.0, -1000.0, 13.0),
            radius = 300.0
        }
    },
    
    -- Victory conditions
    victory = {
        killsRequired = 10,  -- First to 10 kills wins
        timeLimit = 7200,    -- 2 hours
        surrenderOption = true
    }
}

-- Gang Turfs System
Config.GangTurfs = {
    enabled = true,
    
    turfs = {
        ['grove_street'] = {
            name = 'Grove Street',
            coords = vector3(100.0, -1900.0, 21.0),
            radius = 100.0,
            income = 5000, -- Per hour
            captureTime = 900 -- 15 minutes
        },
        ['ballas_territory'] = {
            name = 'Ballas Territory',
            coords = vector3(300.0, -2000.0, 18.0),
            radius = 120.0,
            income = 6000,
            captureTime = 900
        },
        ['vagos_hood'] = {
            name = 'Vagos Hood',
            coords = vector3(500.0, -1500.0, 29.0),
            radius = 110.0,
            income = 5500,
            captureTime = 900
        },
        ['families_block'] = {
            name = 'Families Block',
            coords = vector3(-200.0, -1600.0, 34.0),
            radius = 90.0,
            income = 4500,
            captureTime = 900
        }
    },
    
    -- Turf war mechanics
    turfWar = {
        frequency = 7200000, -- Every 2 hours
        duration = 1800000,  -- 30 minutes
        minParticipants = 3  -- Minimum 3 gang members
    }
}

-- Gang Events
Config.GangEvents = {
    ['conquest'] = {
        name = 'Gang Conquest',
        frequency = 86400000, -- Daily
        duration = 3600000,   -- 1 hour
        objective = 'control_most_territory',
        rewards = {
            winner = 100000,
            second = 50000,
            third = 25000
        }
    },
    
    ['invasion'] = {
        name = 'Gang Invasion',
        frequency = 172800000, -- Every 2 days
        duration = 1800000,    -- 30 minutes
        objective = 'capture_enemy_base',
        rewards = {
            winner = 75000,
            participation = 15000
        }
    },
    
    ['terror'] = {
        name = 'Terror Attack',
        frequency = 259200000, -- Every 3 days
        duration = 2700000,    -- 45 minutes
        objective = 'cause_maximum_chaos',
        rewards = {
            winner = 125000,
            participation = 20000
        }
    }
}

-- HQ Takeover System
Config.HQTakeover = {
    enabled = true,
    
    hqs = {
        ['apd_kavala'] = {
            name = 'APD Kavala HQ',
            coords = vector3(442.5, -982.0, 30.7),
            radius = 100.0,
            captureTime = 1800, -- 30 minutes
            rewards = 200000
        },
        ['apd_pyrgos'] = {
            name = 'APD Pyrgos HQ',
            coords = vector3(-448.0, 6012.0, 31.7),
            radius = 100.0,
            captureTime = 1800,
            rewards = 200000
        },
        ['rnr_hospital'] = {
            name = 'R&R Hospital',
            coords = vector3(300.0, -600.0, 43.0),
            radius = 80.0,
            captureTime = 1200, -- 20 minutes
            rewards = 150000
        }
    },
    
    requirements = {
        minGangMembers = 5,
        maxCops = 3, -- Cannot takeover if more than 3 cops online
        cooldown = 604800000 -- 1 week cooldown
    }
}

-- Gang Progression System
Config.GangProgression = {
    levels = {
        [1] = {
            name = 'Street Gang',
            requirements = { members = 5, money = 100000 },
            benefits = { 'basic_perks' }
        },
        [2] = {
            name = 'Organized Crime',
            requirements = { members = 8, money = 500000, turfs = 1 },
            benefits = { 'advanced_perks', 'cartel_access' }
        },
        [3] = {
            name = 'Criminal Empire',
            requirements = { members = 12, money = 1000000, turfs = 2, cartel = 1 },
            benefits = { 'elite_perks', 'hq_takeover' }
        }
    }
}
