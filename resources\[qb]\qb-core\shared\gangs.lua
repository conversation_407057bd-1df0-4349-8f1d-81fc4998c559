QBShared = QBShared or {}

-- Olympus Altis Life Gang System
QBShared.Gangs = {
    none = {
        label = 'No Gang',
        grades = { ['0'] = { name = 'Unaffiliated' } },
        cartelAccess = false,
        gangBaseAccess = false
    },

    -- Dynamic Gang System - Gangs are created by players
    -- This is a template for gang creation
    template = {
        label = 'Gang Template',
        maxMembers = 15, -- Maximum gang size
        grades = {
            ['0'] = { name = 'Recruit', permissions = { 'basic' } },
            ['1'] = { name = 'Member', permissions = { 'basic', 'invite' } },
            ['2'] = { name = 'Lieutenant', permissions = { 'basic', 'invite', 'kick', 'promote' } },
            ['3'] = { name = 'Leader', isboss = true, permissions = { 'all' } },
        },
        features = {
            cartelCapture = true, -- Can capture cartels
            gangBase = true, -- Can own gang base
            gangShed = true, -- Can own gang shed
            warDeclaration = true, -- Can declare war
            drugTaxation = false, -- Receives drug taxes (only if owning cartel)
            passiveIncome = false, -- Passive income from cartels
        },
        rules = {
            maxWars = 3, -- Maximum simultaneous wars
            warCooldown = 24, -- Hours between war declarations
            cartelCooldown = 30, -- Minutes between cartel attempts
            memberCooldown = 24, -- Hours before rejoining after leaving
        }
    }
}

-- Cartel System Configuration
QBShared.Cartels = {
    ['og_arms'] = {
        name = 'OG Arms',
        location = vector3(-2047.0, 3132.0, 32.0), -- Warzone location
        radius = 95, -- Capture radius in meters
        captureTime = 300, -- 5 minutes to capture
        benefits = {
            rebelDiscount = 15, -- 15% discount at rebel outposts
            taxRate = 15, -- 15% tax on rebel purchases
        },
        permanent = true, -- Always at this location
    },
    ['church'] = {
        name = 'Church',
        location = vector3(-1950.0, 3200.0, 32.0),
        radius = 95,
        captureTime = 300,
        benefits = {
            drugTax = { 'cocaine', 'meth', 'weed' }, -- Rotates with alpha point
            taxRate = 15,
        },
        rotates = true, -- Can rotate locations
    },
    ['alpha_point'] = {
        name = 'Alpha Point',
        location = vector3(-2100.0, 3050.0, 32.0),
        radius = 95,
        captureTime = 300,
        benefits = {
            drugTax = { 'moonshine', 'mushroom', 'heroin' }, -- Rotates with church
            taxRate = 15,
        },
        rotates = true,
    }
}

-- Gang Wars System
QBShared.GangWars = {
    enabled = true,
    maxDuration = 7200, -- 2 hours maximum war duration
    cooldownPeriod = 86400, -- 24 hours between wars
    rules = {
        redNameSystem = true, -- Enemy names appear red
        noEngagementRequired = true, -- Can shoot on sight
        explosivesAllowed = true, -- Can use titans and explosives
        restraintKilling = false, -- Cannot kill restrained enemies
    }
}
