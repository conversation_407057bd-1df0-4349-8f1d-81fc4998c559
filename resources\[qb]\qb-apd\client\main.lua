local QBCore = exports['qb-core']:GetCoreObject()
local PlayerData = {}
local isOnDuty = false
local currentRank = 0
local lethalAuthorized = false
local currentWave = 0
local inIllegalArea = false
local processingTarget = nil
local processingStartTime = 0

-- Initialize
CreateThread(function()
    while true do
        if LocalPlayer.state.isLoggedIn then
            PlayerData = QBCore.Functions.GetPlayerData()
            if PlayerData.job and PlayerData.job.name == 'police' then
                isOnDuty = PlayerData.job.onduty
                currentRank = PlayerData.job.grade.level
                break
            end
        end
        Wait(1000)
    end
end)

-- Events
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    PlayerData = QBCore.Functions.GetPlayerData()
    if PlayerData.job and PlayerData.job.name == 'police' then
        isOnDuty = PlayerData.job.onduty
        currentRank = PlayerData.job.grade.level
    end
end)

RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
    PlayerData.job = JobInfo
    if JobInfo.name == 'police' then
        isOnDuty = JobInfo.onduty
        currentRank = JobInfo.grade.level
    else
        isOnDuty = false
        currentRank = 0
    end
end)

-- APD Duty Toggle
RegisterNetEvent('qb-apd:client:ToggleDuty', function()
    if PlayerData.job and PlayerData.job.name == 'police' then
        TriggerServerEvent('QBCore:ToggleDuty')
        TriggerServerEvent('qb-apd:server:UpdateDutyStatus')
    end
end)

-- Code 3 System (Lights and Sirens)
local code3Active = false
RegisterCommand('code3', function()
    if not isOnDuty then return end
    
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    if vehicle == 0 then return end
    
    code3Active = not code3Active
    
    if code3Active then
        SetVehicleSiren(vehicle, true)
        SetVehicleHasMutedSirens(vehicle, false)
        -- Notify all nearby civilians of Code 3 engagement
        TriggerServerEvent('qb-apd:server:Code3Engagement', GetEntityCoords(PlayerPedId()))
        QBCore.Functions.Notify('Code 3 activated - All nearby civilians are now engaged', 'primary')
    else
        SetVehicleSiren(vehicle, false)
        QBCore.Functions.Notify('Code 3 deactivated', 'primary')
    end
end)

-- Lethal Authorization System
RegisterNetEvent('qb-apd:client:AuthorizeLethal', function(reason, duration)
    if currentRank < 2 and reason ~= 'authorized_by_senior' then
        QBCore.Functions.Notify('You do not have lethal authorization', 'error')
        return
    end
    
    lethalAuthorized = true
    QBCore.Functions.Notify('Lethal force authorized: ' .. reason, 'success')
    
    if duration then
        SetTimeout(duration * 1000, function()
            lethalAuthorized = false
            QBCore.Functions.Notify('Lethal authorization expired', 'error')
        end)
    end
end)

RegisterNetEvent('qb-apd:client:RevokeLethal', function()
    lethalAuthorized = false
    QBCore.Functions.Notify('Lethal authorization revoked', 'error')
end)

-- Processing System
RegisterNetEvent('qb-apd:client:StartProcessing', function(targetId)
    if not isOnDuty then return end
    
    processingTarget = targetId
    processingStartTime = GetGameTimer()
    
    -- Start L.I.S.T. protocol
    exports['qb-menu']:openMenu({
        {
            header = "Processing Menu - L.I.S.T. Protocol",
            isMenuHeader = true,
        },
        {
            header = "L - Check Licenses",
            txt = "Check suspect's licenses",
            params = {
                event = "qb-apd:client:CheckLicenses",
                args = { targetId = targetId }
            }
        },
        {
            header = "I - Search Inventory",
            txt = "Search suspect's inventory (requires probable cause)",
            params = {
                event = "qb-apd:client:SearchInventory",
                args = { targetId = targetId }
            }
        },
        {
            header = "S - Seize Items",
            txt = "Seize illegal items",
            params = {
                event = "qb-apd:client:SeizeItems",
                args = { targetId = targetId }
            }
        },
        {
            header = "T - Issue Ticket",
            txt = "Issue ticket and complete processing",
            params = {
                event = "qb-apd:client:IssueTicket",
                args = { targetId = targetId }
            }
        },
        {
            header = "Request Superior",
            txt = "Request a superior officer",
            params = {
                event = "qb-apd:client:RequestSuperior",
                args = { targetId = targetId }
            }
        },
        {
            header = "Cancel Processing",
            txt = "Cancel processing",
            params = {
                event = "qb-apd:client:CancelProcessing"
            }
        }
    })
end)

-- Check processing time limit
CreateThread(function()
    while true do
        if processingTarget and processingStartTime > 0 then
            local elapsed = (GetGameTimer() - processingStartTime) / 1000 / 60 -- Convert to minutes
            if elapsed >= Config.APD.Processing.maxTime then
                QBCore.Functions.Notify('Processing time limit exceeded - suspect must be pardoned', 'error')
                TriggerServerEvent('qb-apd:server:PardonSuspect', processingTarget, 'time_limit_exceeded')
                processingTarget = nil
                processingStartTime = 0
            end
        end
        Wait(30000) -- Check every 30 seconds
    end
end)

-- Wave System
RegisterNetEvent('qb-apd:client:JoinWave', function(waveNumber, location)
    currentWave = waveNumber
    QBCore.Functions.Notify('Joining Wave ' .. waveNumber .. ' at ' .. location, 'primary')
end)

RegisterNetEvent('qb-apd:client:WaveComplete', function()
    currentWave = 0
    QBCore.Functions.Notify('Wave complete - return to HQ for next wave', 'primary')
end)

-- Illegal Area Detection
CreateThread(function()
    while true do
        if isOnDuty then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local wasInIllegalArea = inIllegalArea
            inIllegalArea = false
            
            -- Check if in any illegal area
            for areaName, areaData in pairs(QBShared.Olympus.Zones.IllegalAreas) do
                local distance = #(playerCoords - areaData.coords)
                if distance <= areaData.radius then
                    inIllegalArea = true
                    if not wasInIllegalArea then
                        QBCore.Functions.Notify('Entered illegal area: ' .. areaData.name, 'error')
                        -- Auto-authorize lethal in illegal areas
                        if currentRank >= 2 then -- Corporal+
                            TriggerEvent('qb-apd:client:AuthorizeLethal', 'illegal_area')
                        end
                    end
                    break
                end
            end
            
            -- Check red zones
            for zoneName, zoneData in pairs(QBShared.Olympus.Zones.RedZones) do
                local distance = #(playerCoords - zoneData.coords)
                if distance <= zoneData.radius then
                    inIllegalArea = true
                    if not wasInIllegalArea then
                        QBCore.Functions.Notify('Entered red zone: ' .. zoneData.name, 'error')
                        -- Auto-authorize lethal in red zones
                        if currentRank >= 2 then -- Corporal+
                            TriggerEvent('qb-apd:client:AuthorizeLethal', 'red_zone')
                        end
                    end
                    break
                end
            end
            
            if wasInIllegalArea and not inIllegalArea then
                QBCore.Functions.Notify('Left illegal area', 'success')
                -- Revoke lethal if it was only authorized for illegal area
                TriggerEvent('qb-apd:client:RevokeLethal')
            end
        end
        Wait(2000)
    end
end)

-- 3:1 Rule Detection
local function CheckThreeToOneRule()
    if not isOnDuty then return end
    
    local playerCoords = GetEntityCoords(PlayerPedId())
    local nearbyOfficers = 0
    local nearbyCivilians = 0
    
    -- Count nearby players
    for _, playerId in ipairs(GetActivePlayers()) do
        local targetPed = GetPlayerPed(playerId)
        if targetPed ~= PlayerPedId() then
            local distance = #(playerCoords - GetEntityCoords(targetPed))
            if distance <= 50.0 then -- 50 meter radius
                local targetData = QBCore.Functions.GetPlayerData(GetPlayerServerId(playerId))
                if targetData and targetData.job then
                    if targetData.job.name == 'police' and targetData.job.onduty then
                        nearbyOfficers = nearbyOfficers + 1
                    else
                        nearbyCivilians = nearbyCivilians + 1
                    end
                end
            end
        end
    end
    
    -- Check if outnumbered 3:1
    local totalOfficers = nearbyOfficers + 1 -- Include self
    if nearbyCivilians >= (totalOfficers * 3) then
        QBCore.Functions.Notify('Outnumbered 3:1 - surrender required or lethal authorized', 'error')
        TriggerEvent('qb-apd:client:AuthorizeLethal', 'three_to_one')
        return true
    end
    
    return false
end

-- Check 3:1 rule periodically during engagements
CreateThread(function()
    while true do
        if isOnDuty and inIllegalArea then
            CheckThreeToOneRule()
        end
        Wait(5000) -- Check every 5 seconds
    end
end)

-- Processing Events
RegisterNetEvent('qb-apd:client:CheckLicenses', function(data)
    local targetId = data.targetId
    QBCore.Functions.TriggerCallback('qb-apd:server:GetPlayerLicenses', function(licenses)
        local licenseText = "Licenses:\n"
        for license, status in pairs(licenses) do
            licenseText = licenseText .. license .. ": " .. (status and "Valid" or "Invalid") .. "\n"
        end

        exports['qb-menu']:openMenu({
            {
                header = "License Check Results",
                txt = licenseText,
                isMenuHeader = true
            },
            {
                header = "Back to Processing",
                params = {
                    event = "qb-apd:client:StartProcessing",
                    args = { targetId = targetId }
                }
            }
        })
    end, targetId)
end)

RegisterNetEvent('qb-apd:client:SearchInventory', function(data)
    local targetId = data.targetId

    -- Check for probable cause
    exports['qb-menu']:openMenu({
        {
            header = "Probable Cause Required",
            txt = "Select reason for search:",
            isMenuHeader = true
        },
        {
            header = "In/Leaving Illegal Area",
            params = {
                event = "qb-apd:client:PerformSearch",
                args = { targetId = targetId, reason = "illegal_area" }
            }
        },
        {
            header = "Visible Illegal Item/Weapon",
            params = {
                event = "qb-apd:client:PerformSearch",
                args = { targetId = targetId, reason = "visible_illegal" }
            }
        },
        {
            header = "Witnessed Illegal Activity",
            params = {
                event = "qb-apd:client:PerformSearch",
                args = { targetId = targetId, reason = "witnessed_activity" }
            }
        },
        {
            header = "Wanted Individual",
            params = {
                event = "qb-apd:client:PerformSearch",
                args = { targetId = targetId, reason = "wanted" }
            }
        },
        {
            header = "Cancel",
            params = {
                event = "qb-apd:client:StartProcessing",
                args = { targetId = targetId }
            }
        }
    })
end)

RegisterNetEvent('qb-apd:client:PerformSearch', function(data)
    TriggerServerEvent('qb-apd:server:SearchPlayer', data.targetId, data.reason)
end)

RegisterNetEvent('qb-apd:client:SeizeItems', function(data)
    local targetId = data.targetId
    TriggerServerEvent('qb-apd:server:SeizeIllegalItems', targetId)
end)

RegisterNetEvent('qb-apd:client:IssueTicket', function(data)
    local targetId = data.targetId

    exports['qb-menu']:openMenu({
        {
            header = "Issue Ticket",
            txt = "Select charges:",
            isMenuHeader = true
        },
        {
            header = "Possession of Illegal Substance",
            txt = "$5,000",
            params = {
                event = "qb-apd:client:AddCharge",
                args = { targetId = targetId, charge = "Possession of Illegal Substance", fine = 5000 }
            }
        },
        {
            header = "Possession of Illegal Weapon",
            txt = "$10,000",
            params = {
                event = "qb-apd:client:AddCharge",
                args = { targetId = targetId, charge = "Possession of Illegal Weapon", fine = 10000 }
            }
        },
        {
            header = "Reckless Driving",
            txt = "$2,500",
            params = {
                event = "qb-apd:client:AddCharge",
                args = { targetId = targetId, charge = "Reckless Driving", fine = 2500 }
            }
        },
        {
            header = "Evading Police",
            txt = "$7,500",
            params = {
                event = "qb-apd:client:AddCharge",
                args = { targetId = targetId, charge = "Evading Police", fine = 7500 }
            }
        },
        {
            header = "Complete Processing",
            params = {
                event = "qb-apd:client:CompleteProcessing",
                args = { targetId = targetId }
            }
        }
    })
end)

RegisterNetEvent('qb-apd:client:AddCharge', function(data)
    TriggerServerEvent('qb-apd:server:AddCharge', data.targetId, data.charge, data.fine)
    TriggerEvent('qb-apd:client:IssueTicket', { targetId = data.targetId })
end)

RegisterNetEvent('qb-apd:client:CompleteProcessing', function(data)
    TriggerServerEvent('qb-apd:server:CompleteProcessing', data.targetId)
    processingTarget = nil
    processingStartTime = 0
    QBCore.Functions.Notify('Processing completed', 'success')
end)

RegisterNetEvent('qb-apd:client:RequestSuperior', function(data)
    TriggerServerEvent('qb-apd:server:RequestSuperior', data.targetId)
    QBCore.Functions.Notify('Superior officer requested', 'primary')
end)

RegisterNetEvent('qb-apd:client:CancelProcessing', function()
    processingTarget = nil
    processingStartTime = 0
    QBCore.Functions.Notify('Processing cancelled', 'error')
end)

-- Exports
exports('IsOnDuty', function()
    return isOnDuty
end)

exports('GetRank', function()
    return currentRank
end)

exports('IsLethalAuthorized', function()
    return lethalAuthorized
end)

exports('GetCurrentWave', function()
    return currentWave
end)

exports('IsProcessing', function()
    return processingTarget ~= nil
end)
